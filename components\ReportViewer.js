function ReportViewer({ report, onBack }) {
  try {
    const [activeSection, setActiveSection] = React.useState('summary');

    const sections = [
      { id: 'summary', title: '项目概述', icon: 'file-text' },
      { id: 'team', title: '创始人和团队背景', icon: 'users' },
      { id: 'product', title: '项目内容和产品', icon: 'box' },
      { id: 'tokenomics', title: '代币经济学', icon: 'coins' },
      { id: 'community', title: '社区分析', icon: 'heart' },
      { id: 'risks', title: '风险因素', icon: 'alert-triangle' },
      { id: 'conclusion', title: '总结', icon: 'check-circle' }
    ];

    const handleSaveToNotion = () => {
      alert('转存到 Notion 功能开发中...');
    };

    const scrollToSection = (sectionId) => {
      const element = document.getElementById(sectionId);
      if (element) {
        element.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start'
        });
        setActiveSection(sectionId);
      }
    };

    if (!report) return null;

    return (
      <div className="flex min-h-screen bg-[var(--background)]" data-name="report-viewer" data-file="components/ReportViewer.js">
        {/* 左侧导航 */}
        <div className="w-64 bg-[var(--surface)] border-r border-[var(--border-color)] fixed h-full overflow-y-auto">
          <div className="p-6">
            <button
              onClick={onBack}
              className="flex items-center text-[var(--text-secondary)] hover:text-[var(--primary-color)] mb-6"
            >
              <div className="icon-arrow-left text-lg mr-2"></div>
              返回首页
            </button>
            
            <h3 className="font-semibold text-[var(--text-primary)] mb-4">报告导航</h3>
            <nav className="space-y-2">
              {sections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => scrollToSection(section.id)}
                  className={`flex items-center px-4 py-2 text-[var(--text-secondary)] hover:text-[var(--primary-color)] rounded-lg transition-all duration-200 cursor-pointer w-full text-left ${
                    activeSection === section.id ? 'bg-yellow-100 text-[var(--primary-color)]' : 'hover:bg-yellow-50'
                  }`}
                >
                  <div className={`icon-${section.icon} text-lg mr-3 flex-shrink-0`}></div>
                  <span className="flex-1">{section.title}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* 右侧内容 */}
        <div className="ml-64 flex-1 p-8">
          <div className="max-w-4xl mx-auto">
            {/* 报告头部 */}
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold mb-3">{report.projectName}研究报告</h1>
              <p className="text-[var(--text-secondary)] mb-4">
                生成时间: {report.date}
              </p>
              <button
                onClick={handleSaveToNotion}
                className="btn-primary flex items-center mx-auto"
              >
                <div className="icon-book text-lg mr-2"></div>
                转存到 Notion
              </button>
            </div>

            {/* 报告内容 */}
            <div className="card">
              <div 
                className="report-content leading-relaxed space-y-6"
                style={{
                  fontSize: '16px',
                  lineHeight: '1.7',
                  color: 'var(--text-primary)'
                }}
                dangerouslySetInnerHTML={{ __html: report.content }}
              />
            </div>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error('ReportViewer component error:', error);
    return null;
  }
}