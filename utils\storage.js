// 本地存储工具函数

const STORAGE_KEY = 'gemini_research_reports';

function saveReports(reports) {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(reports));
  } catch (error) {
    console.error('保存报告失败:', error);
  }
}

function loadReports() {
  try {
    const saved = localStorage.getItem(STORAGE_KEY);
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error('加载报告失败:', error);
    return [];
  }
}

function deleteReport(reportId) {
  try {
    const reports = loadReports();
    const filtered = reports.filter(report => report.id !== reportId);
    saveReports(filtered);
    return filtered;
  } catch (error) {
    console.error('删除报告失败:', error);
    return loadReports();
  }
}

function clearAllReports() {
  try {
    localStorage.removeItem(STORAGE_KEY);
  } catch (error) {
    console.error('清空报告失败:', error);
  }
}

// 导出到 PDF
function exportToPDF(report) {
  try {
    // 创建打印友好的HTML内容
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${report.projectName} - 研究报告</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; }
          h1, h2 { color: #1E3A8A; }
          .header { border-bottom: 2px solid #1E3A8A; padding-bottom: 20px; margin-bottom: 30px; }
          .content { line-height: 1.6; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>${report.projectName}</h1>
          <p>生成时间: ${report.date}</p>
          ${report.website ? `<p>网站: ${report.website}</p>` : ''}
        </div>
        <div class="content">
          ${report.content}
        </div>
      </body>
      </html>
    `;

    // 创建新窗口并打印
    const printWindow = window.open('', '_blank');
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
  } catch (error) {
    console.error('导出PDF失败:', error);
    alert('导出失败，请重试');
  }
}

// 导出到 JSON
function exportToJSON(reports) {
  try {
    const dataStr = JSON.stringify(reports, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `research_reports_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    URL.revokeObjectURL(link.href);
  } catch (error) {
    console.error('导出JSON失败:', error);
    alert('导出失败，请重试');
  }
}