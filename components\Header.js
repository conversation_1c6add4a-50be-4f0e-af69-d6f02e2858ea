function Header({ currentPage, onNavigate, showSettings, onToggleSettings }) {
  try {
    return (
      <header className="bg-[var(--surface)] border-b border-[var(--border-color)] sticky top-0 z-50 glass-effect" data-name="header" data-file="components/Header.js">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-[var(--primary-color)] to-[var(--accent-color)] rounded-lg flex items-center justify-center">
                <div className="icon-brain text-white text-lg"></div>
              </div>
              <h1 className="text-xl font-bold gradient-text">
                Report AI
              </h1>
            </div>
            
            <nav className="flex items-center space-x-6">
              <button
                onClick={() => {
                  onNavigate('home');
                  onToggleSettings(false);
                }}
                className={`flex items-center px-4 py-2 text-[var(--text-secondary)] hover:text-[var(--primary-color)] rounded-lg transition-all duration-200 cursor-pointer ${(currentPage === 'home' || currentPage === 'generating') && !showSettings ? 'text-[var(--primary-color)]' : ''}`}
              >
                <div className="icon-home text-lg mr-2"></div>
                首页
              </button>
              
              <button 
                onClick={() => onToggleSettings(!showSettings)}
                className={`btn-secondary ${showSettings ? 'bg-[var(--primary-color)] text-white' : ''}`}
              >
                <div className="icon-settings text-lg mr-2"></div>
                设置
              </button>
            </nav>
          </div>
        </div>
      </header>
    );
  } catch (error) {
    console.error('Header component error:', error);
    return null;
  }
}