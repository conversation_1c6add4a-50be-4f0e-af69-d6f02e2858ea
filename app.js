class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo.componentStack);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-[var(--background)]">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-[var(--text-primary)] mb-4">出现了一些问题</h1>
            <p className="text-[var(--text-secondary)] mb-4">抱歉，发生了意外错误。</p>
            <button
              onClick={() => window.location.reload()}
              className="btn-primary"
            >
              重新加载页面
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

function App() {
  try {
  const [currentPage, setCurrentPage] = React.useState('home');
  const [isGenerating, setIsGenerating] = React.useState(false);
  const [progress, setProgress] = React.useState(0);
  const [currentReport, setCurrentReport] = React.useState(null);
  const [reports, setReports] = React.useState([]);
  const [showSettings, setShowSettings] = React.useState(false);

    React.useEffect(() => {
      const savedReports = loadReports();
      setReports(savedReports);
    }, []);

    const handleGenerateReport = async (projectData) => {
      try {
        setIsGenerating(true);
        setProgress(0);
        setCurrentPage('generating');

        // 模拟进度更新
        const progressInterval = setInterval(() => {
          setProgress(prev => {
            if (prev >= 90) {
              clearInterval(progressInterval);
              return 90;
            }
            return prev + Math.random() * 10;
          });
        }, 500);

        const report = await generateReport(projectData);
        
        clearInterval(progressInterval);
        setProgress(100);
        
        setTimeout(() => {
          // Extract proper project name
          let projectName = projectData.projectName;
          if (!projectName && projectData.website) {
            projectName = extractDomainName(projectData.website);
          }
          if (!projectName) {
            projectName = '未知项目';
          }
          
          const newReport = {
            id: Date.now().toString(),
            projectName: projectName,
            website: projectData.website,
            contractAddress: projectData.contractAddress,
            content: report,
            createdAt: new Date().toISOString(),
            date: new Date().toLocaleDateString('zh-CN')
          };

          const updatedReports = [newReport, ...reports];
          setReports(updatedReports);
          saveReports(updatedReports);
          setCurrentReport(newReport);
          setCurrentPage('report');
          setIsGenerating(false);
        }, 1000);

      } catch (error) {
        console.error('生成报告失败:', error);
        setIsGenerating(false);
        setCurrentPage('home');
        alert('生成报告失败，请重试');
      }
    };

    const handleViewReport = (report) => {
      setCurrentReport(report);
      setCurrentPage('report');
    };

    const renderPage = () => {
      if (showSettings) {
        return (
          <div className="max-w-2xl mx-auto px-4 py-8">
            <div className="card">
              <h2 className="text-2xl font-bold mb-6">设置</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">报告设置</label>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" defaultChecked />
                      <span className="text-sm">包含技术分析</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" defaultChecked />
                      <span className="text-sm">包含风险评估</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" defaultChecked />
                      <span className="text-sm">包含投资建议</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" defaultChecked />
                      <span className="text-sm">行业分析</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" defaultChecked />
                      <span className="text-sm">市场竞争格局</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" defaultChecked />
                      <span className="text-sm">合约分析</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="mr-2" defaultChecked />
                      <span className="text-sm">市场分析</span>
                    </label>
                  </div>
                </div>
                <div className="pt-4">
                  <button
                    onClick={() => {
                      setShowSettings(false);
                      setCurrentPage('home');
                    }}
                    className="btn-primary"
                  >
                    保存设置
                  </button>
                </div>
              </div>
            </div>
          </div>
        );
      }

      switch (currentPage) {
        case 'home':
          return (
            <div className="max-w-4xl mx-auto px-4 py-8">
              <ProjectForm onSubmit={handleGenerateReport} />
              <HistoryList 
                reports={reports} 
                onViewReport={handleViewReport}
                onDeleteReport={(reportId) => {
                  const updatedReports = reports.filter(r => r.id !== reportId);
                  setReports(updatedReports);
                  saveReports(updatedReports);
                }}
              />
            </div>
          );
        case 'generating':
          return (
            <div className="max-w-2xl mx-auto px-4 py-16">
              <ProgressBar progress={progress} />
            </div>
          );
        case 'report':
          return (
            <ReportViewer 
              report={currentReport}
              onBack={() => setCurrentPage('home')}
            />
          );
        default:
          return null;
      }
    };

    return (
      <div className="min-h-screen bg-[var(--background)]" data-name="app" data-file="app.js">
        <Header 
          currentPage={currentPage}
          onNavigate={setCurrentPage}
          showSettings={showSettings}
          onToggleSettings={setShowSettings}
        />
        {renderPage()}
      </div>
    );
  } catch (error) {
    console.error('App component error:', error);
    return null;
  }
}

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <ErrorBoundary>
    <App />
  </ErrorBoundary>
);