function HistoryList({ reports, onViewReport, onDeleteReport }) {
  try {
    const [searchTerm, setSearchTerm] = React.useState('');

    const filteredReports = reports.filter(report =>
      report.projectName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (report.website && report.website.toLowerCase().includes(searchTerm.toLowerCase()))
    );

    if (reports.length === 0) {
      return (
        <div className="card text-center" data-name="history-empty" data-file="components/HistoryList.js">
          <div className="icon-file-text text-4xl text-[var(--text-secondary)] mb-4"></div>
          <h3 className="text-lg font-semibold mb-2">暂无历史记录</h3>
          <p className="text-[var(--text-secondary)]">
            生成您的第一份项目研究报告
          </p>
        </div>
      );
    }

    return (
      <div data-name="history-list" data-file="components/HistoryList.js">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">历史报告</h2>
          <div className="relative">
            <div className="icon-search absolute left-3 top-1/2 transform -translate-y-1/2 text-[var(--text-secondary)]"></div>
            <input
              type="text"
              placeholder="搜索项目..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input-field pl-10 w-64"
            />
          </div>
        </div>

        <div className="space-y-2 max-w-4xl">
          {filteredReports.map((report, index) => (
            <div
              key={report.id}
              className="card card-hover cursor-pointer group p-3"
              onClick={() => onViewReport(report)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 flex-1">
                  <div className="w-8 h-8 bg-[var(--primary-color)] text-white rounded-full flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-[var(--text-primary)] group-hover:text-[var(--primary-color)] transition-colors duration-200">
                      {report.projectName}研究报告
                    </h3>
                  </div>
                  <div className="text-sm text-[var(--text-secondary)]">
                    {report.date}
                  </div>
                  <div className="flex items-center text-[var(--primary-color)] text-sm font-medium">
                    查看报告
                    <div className="icon-arrow-right text-sm ml-1 group-hover:translate-x-1 transition-transform duration-200"></div>
                  </div>
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteReport(report.id);
                  }}
                  className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-[var(--text-secondary)] hover:text-[var(--error-color)] p-2 ml-4"
                >
                  <div className="icon-trash-2 text-sm"></div>
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredReports.length === 0 && searchTerm && (
          <div className="text-center py-8">
            <div className="icon-search text-4xl text-[var(--text-secondary)] mb-4"></div>
            <h3 className="text-lg font-semibold mb-2">未找到匹配的报告</h3>
            <p className="text-[var(--text-secondary)]">
              尝试使用其他关键词搜索
            </p>
          </div>
        )}
      </div>
    );
  } catch (error) {
    console.error('HistoryList component error:', error);
    return null;
  }
}
// Partial content, replace this line and continue implementing the file.