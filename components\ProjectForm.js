function ProjectForm({ onSubmit }) {
  try {
    const [projectInput, setProjectInput] = React.useState('');

    const handleSubmit = (e) => {
      e.preventDefault();
      
      if (!projectInput.trim()) {
        alert('请输入项目信息');
        return;
      }

      // 尝试解析输入内容
      const formData = {
        projectName: '',
        website: '',
        contractAddress: ''
      };

      // 如果输入看起来像URL
      if (projectInput.includes('http') || projectInput.includes('www.') || projectInput.includes('.com') || projectInput.includes('.org')) {
        formData.website = projectInput;
      } 
      // 如果输入看起来像合约地址（以0x开头）
      else if (projectInput.startsWith('0x')) {
        formData.contractAddress = projectInput;
        formData.projectName = 'Unknown Project';
      } 
      // 其他情况作为项目名称处理
      else {
        formData.projectName = projectInput;
      }

      onSubmit(formData);
    };

    return (
      <div className="card mb-8" data-name="project-form" data-file="components/ProjectForm.js">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold mb-6">
            <span className="gradient-text">Instant, Insight, Impact</span>
          </h2>
          <p className="text-[var(--text-secondary)] text-xl">
            AI-driven reports—accurate, comprehensive, one-click summaries.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="max-w-2xl mx-auto">
            <input
              type="text"
              value={projectInput}
              onChange={(e) => setProjectInput(e.target.value)}
              placeholder="请输入项目信息：网址或项目名称，合约地址，信息越具体越好"
              className="input-field text-lg py-4"
              required
            />
          </div>

          <div className="max-w-md mx-auto">
            <button type="submit" className="btn-primary w-full text-lg py-4">
              <div className="icon-zap text-lg mr-2 inline-block"></div>
              生成报告
            </button>
          </div>
        </form>
      </div>
    );
  } catch (error) {
    console.error('ProjectForm component error:', error);
    return null;
  }
}