<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Report AI - Instant, Insight, Impact</title>
    <meta name="description" content="AI-driven reports—accurate, comprehensive, one-click summaries">
    <meta name="keywords" content="AI报告,项目分析,智能报告,一键生成">
    
    <!-- Open Graph -->
    <meta property="og:title" content="Report AI - Instant, Insight, Impact">
    <meta property="og:description" content="AI-driven reports—accurate, comprehensive, one-click summaries">
    
    <!-- Twitter -->
    <meta name="twitter:title" content="Report AI - Instant, Insight, Impact">
    <meta name="twitter:description" content="AI-driven reports—accurate, comprehensive, one-click summaries">

    <script src="https://resource.trickle.so/vendor_lib/unpkg/react@18/umd/react.production.min.js"></script>
    <script src="https://resource.trickle.so/vendor_lib/unpkg/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://resource.trickle.so/vendor_lib/unpkg/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://resource.trickle.so/vendor_lib/unpkg/lucide-static@0.516.0/font/lucide.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.9/dist/chart.umd.min.js"></script>

    <style type="text/tailwindcss">
    @layer theme {
        :root {
            --primary-color: #1E3A8A;
            --primary-light: #3B82F6;
            --primary-dark: #1E40AF;
            --secondary-color: #64748B;
            --accent-color: #06B6D4;
            --background: #F8FAFC;
            --surface: #FFFFFF;
            --text-primary: #0F172A;
            --text-secondary: #475569;
            --border-color: #E2E8F0;
            --success-color: #10B981;
            --warning-color: #F59E0B;
            --error-color: #EF4444;
        }
    }

    @layer base {
        body {
            font-family: '仿宋', 'FangSong', 'STFangSong', serif;
            background-color: var(--background);
            color: var(--text-primary);
        }
        
        h1, h2, h3, h4, h5, h6 {
            color: var(--text-primary);
            font-weight: 600;
            font-family: '仿宋', 'FangSong', 'STFangSong', serif;
        }
    }

    @layer components {
        .btn-primary {
            @apply px-6 py-3 bg-gradient-to-r from-[var(--primary-color)] to-[var(--primary-light)] text-white rounded-lg font-medium transition-all duration-200 hover:shadow-lg hover:scale-105 focus:outline-none focus:ring-2 focus:ring-[var(--primary-light)] focus:ring-opacity-50;
        }
        
        .btn-secondary {
            @apply px-4 py-2 bg-[var(--surface)] border border-[var(--border-color)] text-[var(--text-secondary)] rounded-lg font-medium transition-all duration-200 hover:border-[var(--primary-color)] hover:text-[var(--primary-color)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-light)] focus:ring-opacity-50;
        }
        
        .card {
            @apply bg-[var(--surface)] rounded-xl shadow-sm border border-[var(--border-color)] p-6 transition-all duration-200;
        }
        
        .card-hover {
            @apply hover:shadow-md hover:border-[var(--primary-color)] hover:border-opacity-30;
        }
        
        .input-field {
            @apply w-full px-4 py-3 border border-[var(--border-color)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-light)] focus:ring-opacity-50 focus:border-[var(--primary-color)] transition-all duration-200;
        }
        
        .progress-bar {
            @apply w-full bg-[var(--border-color)] rounded-full h-2 overflow-hidden;
        }
        
        .progress-fill {
            @apply h-full bg-gradient-to-r from-[var(--primary-color)] to-[var(--accent-color)] transition-all duration-300 ease-out;
        }
        
        .nav-item {
            @apply flex items-center px-4 py-2 text-[var(--text-secondary)] hover:text-[var(--primary-color)] hover:bg-[var(--primary-color)] hover:bg-opacity-5 rounded-lg transition-all duration-200 cursor-pointer;
        }
        
        .nav-item.active {
            @apply text-[var(--primary-color)] bg-[var(--primary-color)] bg-opacity-10;
        }
    }

    @layer utilities {
        .gradient-text {
            @apply bg-gradient-to-r from-[var(--primary-color)] to-[var(--accent-color)] bg-clip-text text-transparent;
        }
        
        .glass-effect {
            @apply backdrop-blur-sm bg-white bg-opacity-80;
        }
        
        .report-content h2 {
            @apply text-xl font-bold text-[var(--primary-color)] mb-6 mt-8 border-b border-[var(--border-color)] pb-3 text-center;
            font-size: 18px;
            font-family: '仿宋', 'FangSong', 'STFangSong', serif;
        }
        
        .report-content h3 {
            @apply text-lg font-bold text-[var(--text-primary)] mb-4 mt-6;
            font-size: 16px;
            font-family: '仿宋', 'FangSong', 'STFangSong', serif;
            line-height: 1.5;
        }
        
        .report-content h3::before {
            content: "▶ ";
            @apply text-[var(--primary-color)] mr-2;
        }
        
        .report-content p {
            @apply mb-4;
            line-height: 1.6;
            text-indent: 2em;
            font-family: '仿宋', 'FangSong', 'STFangSong', serif;
        }
        
        .report-content ul {
            @apply mb-4 pl-6 space-y-2;
            line-height: 1.6;
        }
        
        .report-content li {
            @apply relative;
            font-family: '仿宋', 'FangSong', 'STFangSong', serif;
        }
        
        .report-content li::before {
            content: "•";
            @apply absolute -left-4 text-[var(--primary-color)] font-bold;
        }
        
        .report-content table {
            @apply w-full border-collapse mb-6 shadow-sm;
        }
        
        .report-content th, .report-content td {
            @apply border border-[var(--border-color)] p-3 text-left;
            font-family: '仿宋', 'FangSong', 'STFangSong', serif;
        }
        
        .report-content th {
            @apply bg-[var(--background)] font-semibold text-[var(--text-primary)];
        }
        
        .report-content code {
            @apply bg-[var(--background)] px-2 py-1 rounded text-sm font-mono;
        }
        
        .report-content blockquote {
            @apply border-l-4 border-[var(--primary-color)] pl-4 italic text-[var(--text-secondary)] my-4;
            font-family: '仿宋', 'FangSong', 'STFangSong', serif;
        }
        
        .report-content .key-term {
            @apply italic text-[var(--primary-color)] font-medium;
        }
        
        .report-content .highlight {
            @apply bg-blue-50 text-blue-800 px-1 rounded;
        }
    }
    </style>
</head>
<body>
    <div id="root"></div>
    
    <script>
        const ChartJS = window.Chart;
    </script>
    
    <script type="text/babel" src="components/Header.js"></script>
    <script type="text/babel" src="components/ProjectForm.js"></script>
    <script type="text/babel" src="components/ProgressBar.js"></script>
    <script type="text/babel" src="components/ReportViewer.js"></script>
    <script type="text/babel" src="components/HistoryList.js"></script>
    <script type="text/babel" src="utils/geminiAPI.js"></script>
    <script type="text/babel" src="utils/storage.js"></script>
    <script type="text/babel" src="app.js"></script>
</body>
</html>