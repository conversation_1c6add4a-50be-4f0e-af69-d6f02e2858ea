function ProgressBar({ progress }) {
  try {
    const getProgressMessage = (progress) => {
      if (progress < 20) return '正在分析项目信息...';
      if (progress < 40) return '收集相关数据...';
      if (progress < 60) return '使用 Gemini AI 分析...';
      if (progress < 80) return '生成报告内容...';
      if (progress < 95) return '格式化报告...';
      return '即将完成...';
    };

    return (
      <div className="text-center" data-name="progress-bar" data-file="components/ProgressBar.js">
        <div className="mb-8">
          <div className="w-20 h-20 bg-gradient-to-r from-[var(--primary-color)] to-[var(--accent-color)] rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse">
            <div className="icon-brain text-3xl text-white"></div>
          </div>
          <h2 className="text-2xl font-bold mb-2">正在生成研究报告</h2>
          <p className="text-[var(--text-secondary)] text-lg">
            {getProgressMessage(progress)}
          </p>
        </div>

        <div className="max-w-md mx-auto">
          <div className="progress-bar mb-4">
            <div 
              className="progress-fill"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
          <div className="flex justify-between text-sm text-[var(--text-secondary)]">
            <span>0%</span>
            <span className="font-medium text-[var(--primary-color)]">
              {Math.round(progress)}%
            </span>
            <span>100%</span>
          </div>
        </div>

        <div className="mt-8 grid grid-cols-4 gap-4 max-w-lg mx-auto">
          {[
            { icon: 'search', label: '数据收集' },
            { icon: 'brain', label: 'AI 分析' },
            { icon: 'file-text', label: '内容生成' },
            { icon: 'check', label: '完成' }
          ].map((step, index) => (
            <div key={index} className="text-center">
              <div className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-2 transition-all duration-300 ${
                progress > index * 25 
                  ? 'bg-[var(--primary-color)] text-white' 
                  : 'bg-[var(--border-color)] text-[var(--text-secondary)]'
              }`}>
                <div className={`icon-${step.icon} text-lg`}></div>
              </div>
              <span className="text-xs text-[var(--text-secondary)]">{step.label}</span>
            </div>
          ))}
        </div>
      </div>
    );
  } catch (error) {
    console.error('ProgressBar component error:', error);
    return null;
  }
}